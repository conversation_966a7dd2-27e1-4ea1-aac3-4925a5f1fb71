import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


# Cell 1: Test run_claude_in_sandbox (waits for completion, returns full session)
import asyncio
from agents.claude_e2b import create_sandbox, cleanup_sandbox, run_claude_in_sandbox
from db import db_manager


# Connect to database
await db_manager.connect()

# Create sandbox
sandbox = await create_sandbox()

result = await sandbox.commands.run("cd workspace && git show-branch main roland/hooks")
print(result.stdout)

from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")
